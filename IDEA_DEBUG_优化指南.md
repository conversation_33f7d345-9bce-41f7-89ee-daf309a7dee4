# IDEA Debug启动优化指南

## 已完成的配置优化

### 1. application-dev.yml 优化
- ✅ 禁用JMX (`spring.jmx.enabled: false`)
- ✅ 禁用DevTools热重启和LiveReload
- ✅ 排除不必要的自动配置
- ✅ 优化Redis连接池大小
- ✅ 设置日志级别为WARN
- ✅ 禁用MyBatis SQL日志输出
- ✅ 禁用Actuator监控
- ✅ 禁用登录权限查询 (`isLoginAuthQuery: 0`)
- ✅ 禁用平台计费功能 (`isPlatformFee: 0`)
- ✅ 禁用PCWP登录检查 (`isPCWPLogin: 0`)

### 2. logback-spring.xml 优化
- ✅ 简化日志格式
- ✅ 设置Root日志级别为WARN
- ✅ 第三方库日志级别设为WARN
- ✅ 应用日志保持INFO级别

## 需要手动配置的IDEA设置

### 1. IDEA JVM参数优化 ⭐⭐⭐⭐⭐
**位置**: Help -> Edit Custom VM Options
```
-Xms2048m
-Xmx4096m
-XX:ReservedCodeCacheSize=1024m
-XX:+UseG1GC
-XX:SoftRefLRUPolicyMSPerMB=50
-ea
-XX:CICompilerCount=2
-Dsun.io.useCanonPrefixCache=false
-Djdk.http.auth.tunneling.disabledSchemes=""
-XX:+HeapDumpOnOutOfMemoryError
-XX:-OmitStackTraceInFastThrow
-Xverify:none
```

### 2. 项目Debug运行配置优化 ⭐⭐⭐⭐⭐
**位置**: Run -> Edit Configurations -> MallMaterialServiceApplication

**VM options**:
```
-Xms1024m
-Xmx2048m
-XX:+UseG1GC
-XX:+UseStringDeduplication
-Dspring.jmx.enabled=false
-Dspring.output.ansi.enabled=never
-Djava.awt.headless=true
-Dfile.encoding=UTF-8
-Dspring.main.lazy-initialization=true
```

**Environment variables** (可选):
```
SPRING_PROFILES_ACTIVE=dev
```

### 3. IDEA性能设置优化 ⭐⭐⭐
**位置**: File -> Settings

#### 3.1 Editor设置
- **Code Completion**: 
  - 取消勾选 "Show suggestions as you type"
  - 设置 "Case sensitive completion" 为 "None"

#### 3.2 Build设置
- **Build, Execution, Deployment -> Compiler**:
  - 勾选 "Build project automatically"
  - 设置 "User-local build process heap size" 为 2048MB

#### 3.3 插件优化
**禁用不必要的插件**:
- Database Tools and SQL (如果不用)
- Version Control相关插件 (如果不用Git集成)
- 各种语言支持插件 (只保留Java相关)

### 4. 系统级优化 ⭐⭐
- 将项目目录添加到杀毒软件白名单
- 确保使用SSD硬盘
- 确保有足够内存 (建议16GB+)
- 关闭Windows Defender实时保护 (临时)

## 使用方法

1. **重启IDEA** - 应用JVM参数优化
2. **清理项目** - `mvn clean`
3. **重新导入Maven项目** - 刷新依赖
4. **使用Debug模式启动** - 应该明显感觉到启动速度提升

## 预期效果

- 启动时间减少 **50-70%**
- 控制台日志输出大幅减少
- 内存使用更加合理
- Debug断点响应更快

## 恢复完整功能

如需恢复完整功能进行生产部署，修改以下配置：
```yaml
# application-dev.yml
mall:
  isLoginAuthQuery: 1    # 恢复登录权限查询
  isPlatformFee: 1       # 恢复平台计费功能
  isPCWPLogin: 1         # 恢复PCWP登录检查

# logback-spring.xml
<root level="info">      # 恢复INFO日志级别
```

## 故障排除

如果启动仍然很慢，检查：
1. IDEA是否应用了JVM参数 (Help -> About 查看)
2. 项目是否使用了正确的Profile (dev)
3. 数据库连接是否正常
4. 网络连接是否稳定 (PCWP、Redis、数据库)
5. 磁盘空间是否充足

## 注意事项

⚠️ **开发环境专用**: 这些优化主要针对开发环境，生产环境请恢复相应配置
⚠️ **功能限制**: 部分功能被禁用以提升启动速度，如需使用请临时开启
⚠️ **定期恢复**: 建议定期使用完整配置测试，确保功能正常
