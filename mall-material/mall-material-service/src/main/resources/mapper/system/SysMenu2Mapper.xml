<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="scrbg.meplat.mall.mapper.system.SysMenu2Mapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="scrbg.meplat.mall.entity.system.SysMenu2">
        <id column="menu_id" property="menuId" jdbcType="VARCHAR"/>
        <result column="code" property="code" jdbcType="VARCHAR"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="auth_label" property="authLabel" jdbcType="VARCHAR"/>
        <result column="icon" property="icon" jdbcType="VARCHAR"/>
        <result column="path_url" property="pathUrl" jdbcType="VARCHAR"/>
        <result column="class_code" property="classCode" jdbcType="TINYINT"/>
        <result column="parent_menu_id" property="parentMenuId" jdbcType="VARCHAR"/>
        <result column="level" property="level" jdbcType="TINYINT"/>
        <result column="sort" property="sort" jdbcType="INTEGER"/>
        <result column="state" property="state" jdbcType="TINYINT"/>
        <result column="remarks" property="remarks" jdbcType="VARCHAR"/>
        <result column="gmt_create" property="gmtCreate" jdbcType="TIMESTAMP"/>
        <result column="gmt_modified" property="gmtModified" jdbcType="TIMESTAMP"/>
        <result column="founder_id" property="founderId" jdbcType="VARCHAR"/>
        <result column="founder_name" property="founderName" jdbcType="VARCHAR"/>
        <result column="is_delete" property="isDelete" jdbcType="TINYINT"/>
        <result column="mall_type" property="mallType" jdbcType="TINYINT"/>
        <result column="is_open" property="isOpen" jdbcType="TINYINT"/>
        <result column="show_dev" property="showDev" jdbcType="TINYINT"/>
        <result column="modify_name" property="modifyName" jdbcType="VARCHAR"/>
        <result column="modify_id" property="modifyId" jdbcType="VARCHAR"/>
        <result column="perms" property="perms" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础字段列表 -->
    <sql id="Base_Column_List">
        a.menu_id, a.code, a.title, a.type, a.auth_label, a.icon, a.path_url, a.class_code,
        a.parent_menu_id, a.level, a.sort, a.state, a.remarks, a.gmt_create, a.gmt_modified,
        a.founder_id, a.founder_name, a.is_delete, a.mall_type, a.is_open, a.show_dev,
        a.modify_name, a.modify_id, a.perms
    </sql>

    <!-- 获取菜单树 -->
    <select id="getMenuTree" resultMap="BaseResultMap" parameterType="map">
        SELECT b.name,
        <include refid="Base_Column_List"/>
        FROM sys_menu1 a join sys_syslist b
        on a.class_code = b.id
        WHERE a.is_delete = 0
        <if test="classCode != null">
            AND a.class_code = #{classCode}
        </if>
        <if test="title != null and title != ''">
            AND a.title LIKE CONCAT('%', #{title}, '%')
        </if>
        ORDER BY a.sort ASC
    </select>

    <!-- 分页查询菜单列表 -->
    <select id="getMenuList" resultMap="BaseResultMap" parameterType="map">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sys_menu1
        WHERE is_delete = 0
        <if test="classCode != null">
            AND class_code = #{classCode}
        </if>
        <if test="title != null and title != ''">
            AND title LIKE CONCAT('%', #{title}, '%')
        </if>
        <if test="type != null and type != ''">
            AND type = #{type}
        </if>
        ORDER BY sort ASC
    </select>

    <!-- 新增菜单 -->
    <insert id="addMenu" parameterType="scrbg.meplat.mall.entity.system.SysMenu2">
        INSERT INTO sys_menu1 (
        menu_id, code, title, type, auth_label, icon, path_url,
        class_code, parent_menu_id, level, sort, state, remarks,
        gmt_create, founder_id, founder_name, is_delete, mall_type,
        is_open, show_dev, perms
        ) VALUES (
        #{menuId}, #{code}, #{title}, #{type}, #{authLabel}, #{icon}, #{pathUrl},
        #{classCode}, #{parentMenuId}, #{level}, #{sort}, #{state}, #{remarks},
        #{gmtCreate}, #{founderId}, #{founderName}, #{isDelete}, #{mallType},
        #{isOpen}, #{showDev}, #{perms}
        )
    </insert>

    <!-- 更新菜单 -->
    <update id="updateMenu" parameterType="scrbg.meplat.mall.entity.system.SysMenu2">
        UPDATE sys_menu1
        <set>
            <if test="code != null">code = #{code},</if>
            <if test="title != null">title = #{title},</if>
            <if test="type != null">type = #{type},</if>
            <if test="authLabel != null">auth_label = #{authLabel},</if>
            <if test="icon != null">icon = #{icon},</if>
            <if test="pathUrl != null">path_url = #{pathUrl},</if>
            <if test="classCode != null">class_code = #{classCode},</if>
            <if test="parentMenuId != null">parent_menu_id = #{parentMenuId},</if>
            <if test="level != null">level = #{level},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="state != null">state = #{state},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
            <if test="modifyName != null">modify_name = #{modifyName},</if>
            <if test="modifyId != null">modify_id = #{modifyId},</if>
            <if test="mallType != null">mall_type = #{mallType},</if>
            <if test="isOpen != null">is_open = #{isOpen},</if>
            <if test="showDev != null">show_dev = #{showDev},</if>
            <if test="perms != null">perms = #{perms},</if>
        </set>
        WHERE menu_id = #{menuId}
    </update>
    <!-- 删除菜单 -->
    <update id="deleteMenu" parameterType="scrbg.meplat.mall.entity.system.SysMenu2">
        UPDATE sys_menu1
        SET is_delete = -1,
        gmt_modified = #{gmtModified},
        modify_id = #{modifyId},
        modify_name = #{modifyName}
        WHERE menu_id = #{menuId}
    </update>

    <!-- 获取菜单详情 -->
    <select id="getMenuDetail" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sys_menu1
        WHERE menu_id = #{menuId} AND is_delete = 0
    </select>

    <!-- 检查菜单编码是否已存在 -->
    <select id="checkCodeExists" resultType="int">
        SELECT COUNT(1) FROM sys_menu1
        WHERE code = #{code} AND is_delete = 0
        <if test="menuId != null and menuId != ''">
            AND menu_id != #{menuId}
        </if>
    </select>

    <select id="getPerms" resultType="int">
        SELECT COUNT(1) FROM sys_menu1
        WHERE perms = #{perms}
        <if test="menuId != null and menuId != ''">
            AND menu_id != #{menuId}
        </if>
    </select>

</mapper>