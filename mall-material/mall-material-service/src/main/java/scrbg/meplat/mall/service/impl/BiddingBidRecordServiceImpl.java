package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import scrbg.meplat.mall.common.constant.ProcessConstants;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.dto.bidding.AuditBidingDTO;
import scrbg.meplat.mall.dto.bidding.AuditBidingInfoDTO;
import scrbg.meplat.mall.dto.bidding.BatchUpdateBiddingItemInfoDTO;
import scrbg.meplat.mall.dto.bidding.BidOfferInfoVo;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.BiddingBidRecordMapper;
import scrbg.meplat.mall.service.*;
import scrbg.meplat.mall.util.DateUtil;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;
import scrbg.meplat.mall.util.countExcel.ExcelForWebUtil;
import scrbg.meplat.mall.vo.bidding.BidSummarySubVo;
import scrbg.meplat.mall.vo.bidding.BiddingBidRecordVO;
import scrbg.meplat.mall.vo.product.StationMessageReceiveVO;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @描述：竞价记录 服务类
 * @作者: ye
 * @日期: 2023-07-11
 */
@Service
public class BiddingBidRecordServiceImpl extends ServiceImpl<BiddingBidRecordMapper, BiddingBidRecord> implements BiddingBidRecordService {
    @Resource
    private BiddingProductService biddingProductService;
    @Autowired
    private MallConfig mallConfig;
    @Resource
    private BiddingPurchaseService biddingPurchaseService;
    @Resource
    private SynthesizeTemporaryService synthesizeTemporaryService;

    @Autowired
    BiddingBidRecordMapper bidRecordMapper;

    @Autowired
    ProcessConfigService processConfigService;

    @Autowired
    ProcessInstanceService processInstanceService;

    @Autowired
    AuditService auditService;

    @Autowired
    BiddingBidRecordItemService recordItemService;

    @Autowired
    private BiddingWinRecordService biddingWinRecordService;

    @Autowired
    FileService fileService;

    @Autowired
    StationMessageService stationMessageService;

    @Autowired
    ProcessNodeOperationService processNodeOperationService;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<BiddingPurchase> q) {
        q.eq(BiddingPurchase::getCreateOrgId, ThreadLocalUtil.getCurrentUser().getEnterpriseId());
        String keywords = (String) jsonObject.get("keywords");
        String biddingSn = (String) jsonObject.get("biddingSn");
        String title = (String) jsonObject.get("title");
        String startDate = (String) jsonObject.get("startDate");
        String endDate = (String) jsonObject.get("endDate");
        String linkName = (String) jsonObject.get("linkName");
        String linkPhone = (String) jsonObject.get("linkPhone");
        Integer state = (Integer) jsonObject.get("state");
        Integer orderBy = (Integer) jsonObject.get("orderBy");
        Integer productType = (Integer) jsonObject.get("productType");
        String createStartDate = (String) jsonObject.get("createStartDate");
        String createEndDate = (String) jsonObject.get("createEndDate");
        Integer biddingState = (Integer) jsonObject.get("biddingState");
        Integer publicityState = (Integer) jsonObject.get("publicityState");
        Integer type = (Integer) jsonObject.get("type");
        if (orderBy != null) {
            if (orderBy == 1) {
                q.orderByDesc(BiddingPurchase::getGmtCreate);
            }
            if (orderBy == 2) {
                q.orderByDesc(BiddingPurchase::getStartTime);
            }
            if (orderBy == 3) {
                q.orderByDesc(BiddingPurchase::getEndTime);
            }
        }
        if (state == null) {
            q.in(BiddingPurchase::getState, 4, 7, 8, 9, 6, 10);
        } else {
            q.eq(BiddingPurchase::getState, state);
        }
        q.select(BiddingPurchase.class, f -> {
            return !f.getProperty().equals("biddingExplain") && !f.getProperty().equals("remarks");
        });

        q.eq(biddingState != null, BiddingPurchase::getBiddingState, biddingState);
        q.eq(productType != null, BiddingPurchase::getProductType, productType);
        q.eq(type != null, BiddingPurchase::getType, type);
        q.eq(publicityState != null, BiddingPurchase::getPublicityState, publicityState);
        if (StringUtils.isNotBlank(biddingSn)) q.like(BiddingPurchase::getBiddingSn, biddingSn.trim());
        if (StringUtils.isNotBlank(title)) q.like(BiddingPurchase::getTitle, title.trim());
        if (StringUtils.isNotBlank(linkName)) q.like(BiddingPurchase::getLinkName, linkName.trim());
        if (StringUtils.isNotBlank(linkPhone)) q.like(BiddingPurchase::getLinkPhone, linkPhone.trim());
        q.between(StringUtils.isNotEmpty(startDate) && StringUtils.isNotEmpty(endDate), BiddingPurchase::getEndTime, startDate, endDate);
        q.between(StringUtils.isNotEmpty(createStartDate) && StringUtils.isNotEmpty(createEndDate), BiddingPurchase::getGmtCreate, createStartDate, createEndDate);
        if (!org.springframework.util.StringUtils.isEmpty(keywords)) {
            q.and((t) -> {
                t.like(BiddingPurchase::getBiddingSn, keywords)
                        .or()
                        .like(BiddingPurchase::getTitle, keywords)
                        .or()
                        .like(BiddingPurchase::getSynthesizeTemporarySn, keywords);
            });
        }
        q.orderByDesc(BiddingPurchase::getGmtCreate);
        IPage<BiddingPurchase> page = biddingPurchaseService.page(
                new Query<BiddingPurchase>().getPage(jsonObject),
                q
        );
        List<BiddingPurchase> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new PageUtils(page);
        } else {
            for (BiddingPurchase record : records) {
                Date endTime = record.getEndTime();
                // 如果截止时间小于当前时间
                if (endTime.compareTo(new Date()) < 0) {
                    // 结束了
                    if (record.getBiddingState() != 3) {
                        biddingPurchaseService.lambdaUpdate().eq(BiddingPurchase::getBiddingId, record.getBiddingId())
                                .set(BiddingPurchase::getBiddingState, 3)
                                .set(BiddingPurchase::getGmtModified, new Date()).update();
                        record.setBiddingState(3);
                    }
                } else {
                    // 没有到达结束时间
                    // 判断是否通过并且发布了，修改状态为进行中
                    if (record.getState() == 5) {
                        if (record.getBiddingState() != 2) {
                            biddingPurchaseService.lambdaUpdate().eq(BiddingPurchase::getBiddingId, record.getBiddingId())
                                    .set(BiddingPurchase::getBiddingState, 2)
                                    .set(BiddingPurchase::getGmtModified, new Date()).update();
                            record.setBiddingState(2);
                        }
                    }
                }
            }
            return new PageUtils(page);
        }
    }

    @Override
    public void create(BiddingBidRecord biddingBidRecord) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(biddingBidRecord);
    }

    @Override
    public void update(BiddingBidRecord biddingBidRecord) {
        super.updateById(biddingBidRecord);
    }


    @Override
    public BiddingBidRecord getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    @Override
    public List<BiddingBidRecord> selectListByBiddingId(String biddingId) {
        LambdaQueryWrapper<BiddingBidRecord> eq = new LambdaQueryWrapper<BiddingBidRecord>().eq(BiddingBidRecord::getBiddingId, biddingId);
        List<BiddingBidRecord> list = list(eq);
        return list;
    }


    @Override
    public void updateAuditStatus(AuditBidingDTO auditBidingDTO) {
        UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
        switch (auditBidingDTO.getPageType()) {
            case 1:
                AuditBidingInfoDTO auditBidingInfoDTO = new AuditBidingInfoDTO();
                auditBidingInfoDTO.setBidRecordId(auditBidingDTO.getBidRecordId());
                auditBidingInfoDTO.setAuditResult(auditBidingDTO.getAuditResult());
                auditBidingInfoDTO.setBiddingId(auditBidingDTO.getBiddingId());
                auditBidingInfoDTO.setIsOpen(auditBidingDTO.getAuditResultType());
                auditBidingInfoDTO.setAuditProcessType(auditBidingDTO.getAuditProcessType());
                biddingPurchaseService.auditHitBidding(auditBidingInfoDTO);
                break;
            case 2:
                BiddingPurchase purchase = biddingPurchaseService.getById(auditBidingDTO.getBiddingId());
                if (purchase.getState() == 6)
                    biddingPurchaseService.lambdaUpdate().eq(BiddingPurchase::getBiddingId, auditBidingDTO.getBiddingId())
                            .set(BiddingPurchase::getCancelResultType, auditBidingDTO.getCancelResultType())
                            .set(BiddingPurchase::getCancelRemark, auditBidingDTO.getAuditResult())
                            .set(BiddingPurchase::getState, 10).update();
                break;
            case 3:
                //撤回
                ProcessInstance instance = processInstanceService.lambdaQuery().eq(ProcessInstance::getBusinessKey, auditBidingDTO.getBiddingId()).one();

                List<ProcessNodeOperation> list = processNodeOperationService.lambdaQuery().
                        eq(ProcessNodeOperation::getProcessInstanceId, instance.getProcessInstanceId()).orderByDesc(ProcessNodeOperation::getGmtCreate).list();
                if (list.size() > 0)
                    processNodeOperationService.delete(list.get(0).getProcessNodeOperationId());
                break;
            case 4:
                updateRecordAuditStatus(currentUser, auditBidingDTO);
                break;
        }
    }

    private void updateRecordAuditStatus(UserLogin currentUser, AuditBidingDTO auditBidingDTO) {
        String auditResult = auditBidingDTO.getBiddingRemark();
        if (auditResult != null) {
            if (auditResult.length() > 2500) {
                throw new BusinessException("竞价说明内容过大！");
            }
        }
        BiddingPurchase biddingPurchase = new BiddingPurchase();

        biddingPurchase.setBiddingId(auditBidingDTO.getBiddingId());
        biddingPurchase.setOperatorId(auditBidingDTO.getOperatorId());
        biddingPurchase.setOperator(auditBidingDTO.getOperator());
        biddingPurchase.setBiddingOrderCompany(auditBidingDTO.getBiddingOrderCompany());
        biddingPurchase.setBiddingExplain(auditResult);

        biddingPurchaseService.update(biddingPurchase);
        List<BidOfferInfoVo> bidOfferInfoVos = auditBidingDTO.getBidOfferInfoVos();

        if (!CollectionUtils.isEmpty(bidOfferInfoVos)) {
            lambdaUpdate().eq(BiddingBidRecord::getBiddingId, auditBidingDTO.getBiddingId())
                    .set(BiddingBidRecord::getBidState, 0).update();
            BidOfferInfoVo infoVo = bidOfferInfoVos.stream().filter(e -> e.getBidState() != null && e.getBidState() == 1).collect(Collectors.toList()).get(0);
            lambdaUpdate().eq(BiddingBidRecord::getBidRecordId, infoVo.getBidRecordId())
                    .set(BiddingBidRecord::getBidState, 1).update();

            for (BidOfferInfoVo d : bidOfferInfoVos) {
//                Integer count = biddingPurchaseService.lambdaQuery().eq(BiddingPurchase::getBiddingId, d.getBiddingId())
//                        .in(BiddingPurchase::getState, 0, 2).count();
//                if (count == 0) {
//                    continue;
//                }
                BiddingProduct biddingProduct = new BiddingProduct();
                biddingProduct.setBiddingProductId(d.getBiddingProductId());
                biddingProduct.setMonthlyDifference(d.getMonthlyDifference());
                biddingProduct.setUnitPriceIncludingTax(d.getUnitPriceIncludingTax());
                biddingProduct.setTaxInclusiveAmount(d.getTaxInclusiveAmount());
                biddingProductService.update(biddingProduct);
            }
        }
        Integer isSubmit = auditBidingDTO.getIsSubmit();
        if (isSubmit != null && isSubmit == 1) {
            boolean bb = ThreadLocalUtil.getCurrentUser().getRoles().contains("物资竞价提交权限");
            if (!bb) {
                throw new BusinessException("没有权限请联系管理员！");
            }
//            lambdaUpdate().eq(BiddingBidRecord::getBidRecordId, auditBidingDTO.getBidRecordId())
//                    .set(BiddingBidRecord::getState, 5).update();
            biddingPurchaseService.lambdaUpdate().eq(BiddingPurchase::getBiddingId, auditBidingDTO.getBiddingId())
                    .set(BiddingPurchase::getState, 6).update();

            //更新工作流
            if (auditBidingDTO.getAuditProcessType() == 1) {
                processConfigService.myFunc(ProcessConstants.BIDDING_PROCESS_ID_FIRST,
                        currentUser, 0, auditBidingDTO.getBiddingId(), "【同意】");
            } else {
                processConfigService.myFunc(ProcessConstants.BIDDING_PROCESS_ID_NO_FIRST,
                        currentUser, 0, auditBidingDTO.getBiddingId(), "【同意】");
            }

        }
    }


    @Override
    public BiddingBidRecordVO getBiddingRecordInfo(String biddingId) {
        BiddingBidRecordVO vo = new BiddingBidRecordVO();
        Map<String, Object> map = new ConcurrentHashMap<>();
        List<BidOfferInfoVo> productListSort = new ArrayList<>();
        map.put("materialsData", productListSort);
        map.put("materialsNum", 0);
        List<BiddingBidRecord> bidRecords = lambdaQuery().eq(BiddingBidRecord::getBiddingId, biddingId).list();
        BiddingPurchase biddingPurchase = biddingPurchaseService.getById(biddingId);
        // 获取主体信息
        BeanUtils.copyProperties(biddingPurchase, vo);
        vo.setBiddingBidRecordsMap(map);
        if (bidRecords.isEmpty()) return vo;

        List<BidOfferInfoVo> productList = new ArrayList<>();
        for (BiddingBidRecord bidRecord : bidRecords) {

            bidRecord.setProductType(biddingPurchase.getProductType());
            //存储附件信息
            File file = fileService.lambdaQuery()
                    .eq(File::getRelevanceId, bidRecord.getBidRecordId())
                    .eq(File::getRelevanceType, 15).one();
            if (file != null) {
                bidRecord.setFileFarId(file.getFileFarId());
                bidRecord.setFileName(file.getName());
            }

            List<BiddingBidRecordItem> itemList = recordItemService.lambdaQuery()
                    .eq(BiddingBidRecordItem::getBidRecordId, bidRecord.getBidRecordId())
                    .orderByDesc(BiddingBidRecordItem::getGmtModified).list();

            //根据明细表的竞价采购商品ID 查询竞价商品表的商品数据
            for (BiddingBidRecordItem item : itemList) {
                BidOfferInfoVo info = packageRecordVo(bidRecord, item);
                productList.add(info);
            }
        }

        Map<String, List<BidOfferInfoVo>> groupByGrade = productList.stream()
                .collect(Collectors.groupingBy(BidOfferInfoVo::getBidRecordId));
        int xh = 0;

        for (String key : groupByGrade.keySet()) {
            ++xh;
            BidOfferInfoVo bidOfferInfoVo = new BidOfferInfoVo();
            List<BidOfferInfoVo> bidOfferInfoVos = groupByGrade.get(key);
            map.put("materialsNum", groupByGrade.get(key).size());
            int finalXh = xh;
            List<BidOfferInfoVo> collect = bidOfferInfoVos.stream().peek(e -> {
                e.setXh(finalXh);
            }).collect(Collectors.toList());
            double sum = collect.stream()
                    .mapToDouble(e -> e.getBidRateAmount().doubleValue())
                    .sum();
            bidOfferInfoVo.setProductName("小计");
            bidOfferInfoVo.setUnit(sum + "");
            collect.add(bidOfferInfoVo);
            productListSort.addAll(collect);

        }
        map.put("materialsData", productListSort);
        vo.setBiddingBidRecordsMap(map);


        //封装审核历史记录
        List<AuditRecords> auditRecords = getAuditRecords(biddingPurchase);
        vo.setAuditRecords(auditRecords);


        List<BiddingWinRecord> winRecords = biddingWinRecordService.lambdaQuery().eq(BiddingWinRecord::getBiddingId, biddingId).list();
        List<BidOfferInfoVo> winningBidResult = new ArrayList();
        if (winRecords.size() > 0) {
            BiddingBidRecord bidRecord = lambdaQuery().eq(BiddingBidRecord::getBiddingId, biddingId).eq(BiddingBidRecord::getBidState, 1).one();
            bidRecord.setProductType(biddingPurchase.getProductType());
            BiddingWinRecord biddingWinRecord = winRecords.get(0);

            List<BiddingBidRecordItem> itemList1 = recordItemService.lambdaQuery()
                    .eq(BiddingBidRecordItem::getBidRecordId, bidRecord.getBidRecordId())
                    .orderByDesc(BiddingBidRecordItem::getGmtModified).list();

            //根据明细表的竞价采购商品ID 查询竞价商品表的商品数据
            for (BiddingBidRecordItem item : itemList1) {
                BidOfferInfoVo bidOfferInfoVo = packageRecordVo(bidRecord, item);
                winningBidResult.add(bidOfferInfoVo);
            }

            double sum = winningBidResult.stream()
                    .mapToDouble(e -> e.getBidRateAmount().doubleValue())
                    .sum();
            BidOfferInfoVo bidOfferInfoVo = new BidOfferInfoVo();
            bidOfferInfoVo.setProductName("小计");
            bidOfferInfoVo.setUnit(sum + "");
            winningBidResult.add(bidOfferInfoVo);
        }

        vo.setWinningBidResult(winningBidResult);

        return vo;
    }

    @NotNull
    private BidOfferInfoVo packageRecordVo(BiddingBidRecord bidRecord, BiddingBidRecordItem item) {
        BidOfferInfoVo info = new BidOfferInfoVo();
        info.setId(UUID.randomUUID().toString());
        info.setSupplierName(bidRecord.getSupplierName());
        info.setBidRecordId(bidRecord.getBidRecordId());
        info.setBidState(bidRecord.getBidState() == null ? 0 : bidRecord.getBidState());
        // 组装竞价明细
        info.setBidPrice(item.getBidPrice());
        info.setState(bidRecord.getState());
        info.setRemarks(item.getRemarks());
        info.setTaxRate(item.getTaxRate());
        info.setBidRatePrice(item.getBidRatePrice());
        info.setBidRateAmount(item.getBidRateAmount());
        info.setBidAmount(item.getBidAmount());
        //info.setRemarks(item.getRemarks());
        String productId = item.getBiddingProductId();
        // 组装物资明细
        BiddingProduct product = biddingProductService.lambdaQuery()
                .eq(BiddingProduct::getBiddingProductId, productId)
                //.eq(BiddingProduct::getBiddingSn,biddingSn)
                .one();
        info.setDeliveryAddress(product.getDeliveryAddress());
        Date deliveryDate = product.getDeliveryDate();
        if (deliveryDate == null) {
            info.setDeliveryDate(null);
        } else {
            info.setDeliveryDate(DateUtil.getyyymmdd(deliveryDate));
        }
        info.setUnit(product.getUnit());
        info.setProductName(product.getProductName());
        info.setSpec(product.getSpec());
        info.setBiddingProductId(product.getBiddingProductId());
        info.setProductTexture(product.getProductTexture());
        info.setNum(product.getNum());
        // 限价
        info.setReferencePrice(product.getReferencePrice());
        //大宗 和周转
        if (bidRecord.getProductType() == 1 || bidRecord.getProductType() == 2) {
            if (bidRecord.getBillType() == 1) {
                // 浮动
                info.setNetPrice(item.getNetPrice());
                info.setFixationPrice(item.getFixationPrice());
            }
            if (bidRecord.getBillType() == 2) {
                // 固定
                info.setOutFactoryPrice(item.getOutFactoryPrice());
                info.setTransportPrice(item.getTransportPrice());
            }
        }
        return info;
    }

    private List<AuditRecords> getAuditRecords(BiddingPurchase purchase) {

        List<AuditRecords> auditRecordsList = new ArrayList<>();
        List<ProcessInstance> processInstancesFirst = processInstanceService.lambdaQuery().eq(ProcessInstance::getBusinessKey, purchase.getBiddingId()).list();
        if (!processInstancesFirst.isEmpty()) {
            ProcessInstance instance = processInstancesFirst.get(0);
            List<AuditRecords> records = auditService.findRecordsBy(instance.getProcessInstanceId());
            auditRecordsList.addAll(records);
        }
        return auditRecordsList;

    }

    @Override
    public void exportBidSummary(String bidSn, HttpServletResponse response) {
        if (StringUtils.isBlank(bidSn)) {
            throw new BusinessException("未携带竞价编号");
        }
        Map<String, Object> data = new HashMap<>();
        // 竞价数据
        BiddingPurchase purchase = biddingPurchaseService.lambdaQuery()
                .eq(BiddingPurchase::getBiddingSn, bidSn)
                .and(wra -> {
                    wra.eq(BiddingPurchase::getBiddingState, 3).or(rapper -> {
                        rapper.lt(BiddingPurchase::getEndTime, new Date());
                    });
                })
                .one();
        if (ObjectUtils.isEmpty(purchase)) {
            throw new BusinessException("竞价未结束，无法导出");
        }
        // 中标待审核的记录 ？ 是否
        BiddingBidRecord record = super.lambdaQuery().eq(BiddingBidRecord::getBiddingSn, bidSn).eq(BiddingBidRecord::getState, 5).one();
        String hitSupplier = null;
        if (ObjectUtils.isEmpty(record)) {
            //throw new BusinessException("未选择中标供货商");
            hitSupplier = "";

        } else {
            hitSupplier = record.getSupplierName();
        }
        data.put("hitSupplier", hitSupplier);
        // 中标供货商

        data.put("title", purchase.getCreateOrgName());
        data.put("bidSn", purchase.getBiddingSn());
        // 时间
        Date endTime = purchase.getEndTime();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String endStr = format.format(endTime);
        String[] strings = endStr.split("-");
        data.put("year", strings[0]);
        data.put("month", strings[1]);
        data.put("day", strings[2]);
        // 报价数据
        List<BidSummarySubVo> vos = super.baseMapper.getBidSummaryWithBidRecordId(bidSn);
        List<BidSummarySubVo> newVos = new ArrayList<>();
        Map<String, List<BidSummarySubVo>> collect = vos.stream().collect(Collectors.groupingBy(BidSummarySubVo::getSupplierName));
        collect.forEach((supplier, list) -> {
            // 循环放入
            newVos.addAll(list);
            BigDecimal amount = new BigDecimal(0);
            for (BidSummarySubVo bidSummarySubVo : list) {
                BigDecimal bidRateAmount = bidSummarySubVo.getBidRateAmount();
                amount = amount.add(bidRateAmount);
            }
            BidSummarySubVo bidSummarySubVo = new BidSummarySubVo();
            bidSummarySubVo.setSupplierName(supplier);
            bidSummarySubVo.setProductName("小计");
            bidSummarySubVo.setBidRateAmount(amount);
            newVos.add(bidSummarySubVo);
        });
        ArrayList<BidSummarySubVo> list = Lists.newArrayList(newVos);
        data.put("dataList", list);
        //String src = "/Volumes/westDisk/work/template/竞价模板";
        String src = mallConfig.templateFormUrl;
        if (purchase.getBiddingSourceType() == 3) {
            // 项目部名称
            String synthesizeTemporarySn = purchase.getSynthesizeTemporarySn();
            SynthesizeTemporary synthesizeTemporary = synthesizeTemporaryService.lambdaQuery().eq(SynthesizeTemporary::getSynthesizeTemporarySn, synthesizeTemporarySn).select(SynthesizeTemporary::getOrgName).one();
            if (synthesizeTemporary != null) {
                data.put("title", synthesizeTemporary.getOrgName());
            }
            try {
                ExcelForWebUtil.exportExcel(response, data, "大宗临购清单竞价汇总模板.xlsx", src, purchase.getTitle() + "竞价汇总.xlsx");
                return;
            } catch (Exception e) {
                throw new BusinessException("导出异常" + e.getMessage());
            }
        }
        try {
            ExcelForWebUtil.exportExcel(response, data, "竞价汇总表模板.xlsx", src, purchase.getTitle() + "竞价汇总.xlsx");
        } catch (Exception e) {
            throw new BusinessException("导出异常" + e.getMessage());
        }
    }
}
