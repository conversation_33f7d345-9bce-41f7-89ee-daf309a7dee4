spring:
  # 启动优化配置
  jmx:
    enabled: false  # 禁用JMX以加快启动速度
  devtools:
    restart:
      enabled: false  # 禁用热重启
    livereload:
      enabled: false  # 禁用LiveReload
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.admin.SpringApplicationAdminJmxAutoConfiguration
      - org.springframework.boot.autoconfigure.jmx.JmxAutoConfiguration
  rabbitmq:
    tt:
#      host: **************
      host: *************       #这里由于用户加了IP访问限制，所以用了IP+端口转发模式
#      port: 5672
      port: 80                  #这里由于用户加了IP访问限制，所以用了IP+端口转发模式
      username: wzsc                                    # TT待办系统用户名
      password: FABF26F6-7615-EB92-E158-09F3125DC089   # TT待办系统密码
      virtual-host: ToDoList
    sm: #短信
      host: ************** # 主机名
      port: 5672 # 端口
      virtual-host: / # 虚拟主机
      username: mmcp # 用户名
      password: Ms1q2w3e # 密码
    mdm: # 人员和组织数据同步
      host: *************  #这里由于用户加了IP访问限制，所以用了IP+端口转发模式
      port: 80            #这里由于用户加了IP访问限制，所以用了IP+端口转发模式
      username: wzsc
      password: FABF26F6-7615-EB92-E158-09F3125DC089
      virtual-host: mdm
      exchange: MDM.V2.RYKJK # 交换器名称
      isSync: false
      queues:
        - name: Personinfo_Full_dev
          routingKey: Personinfo_Full
        - name: Zcinfo_Full_dev
          routingKey: Zcinfo_Full
        - name: Zyzginfo_Full_dev
          routingKey: Zyzginfo_Full
        - name: Grhjinfo_Full_dev
          routingKey: Grhjinfo_Full
        - name: Rzjlinfo_Full_dev
          routingKey: Rzjlinfo_Full
        - name: Orginfo_Full_dev
          routingKey: Orginfo_Full
#        - name: Sys_Org_Full_dev
#          routingKey: Sys_Org_Full
#        - name: Sys_ConOrg_Full_dev
#          routingKey: Sys_ConOrg_Full
#        - name: Project_Project_Full_dev
#          routingKey: Project_Project_Full
        - name: TTAccount_Full_dev
          routingKey: TTAccount_Full
  redis:
    host: *************
    port: 6380
    password: 9ImAtEIE
    database: 0
    lettuce:
      pool:
        max-active: 8            # 减少最大连接数以加快启动
        max-idle: 4              # 减少最大空闲连接数
        min-idle: 2              # 减少最小空闲连接数
        max-wait: 3000ms         # 减少获取连接的最大等待时间
    timeout: 3000ms              # 减少连接超时时间
  cache:
    type: redis
    redis:
      #      time-to-live: 3600s # 过期时间
      # key-prefix: CACHE_ # 会导致自己在@Cacheable里设置的名字失效，
      use-key-prefix: true # key值加前缀，不声明默认是使用区域作为前缀
      cache-null-values: true # 缓存控制
  #数据源配置
  datasource:
    username: root
#    password: root
    password: 9ImAtEIE
    url: ****************************************************************************************************************************************************************************************************
#    url: *******************************************************************************************************************************************************************
    driver-class-name: com.p6spy.engine.spy.P6SpyDriver

  #云配置
  cloud:
    nacos:
      discovery:
        server-addr: *************:8849
        ip: localhost
        username: nacos
        password: 9ImAtEIE
# redission配置，这里直接读取的redis变量.
#redisson:
#  singleserverconfig:
#    address: "redis://${spring.redis.host}:${spring.redis.port}"
#    password: ${spring.redis.password}
#    database: 0

# 日志优化配置 - 减少启动时的日志输出
logging:
  level:
    root: WARN
    scrbg.meplat.mall: INFO
    org.springframework: WARN
    org.mybatis: WARN
    com.alibaba.druid: WARN
    org.apache.http: WARN
    org.springframework.cloud: WARN
    com.netflix: WARN
    org.springframework.boot.autoconfigure: WARN
    org.springframework.context: WARN
    org.springframework.beans: WARN
    org.springframework.web: WARN
    io.lettuce: WARN
    com.zaxxer.hikari: WARN

# MyBatis优化 - 禁用SQL日志输出
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl

# 禁用Actuator监控以加快启动
management:
  endpoints:
    enabled-by-default: false

mall:
  prodPcwp2Url: "http://**************:15101" # 这个验证登录走的是PCWP云中心开具的权限，这里对应的是局域网IP地址：http://***********:5000/
  prodPcwp2Url02: "http://**************:15103"
  thirdApiToken: "UXp0UjJET2csMTY3MTA0MjIzMw.H83JNfMc8fze5OF-Vn4oSF4SqF4Wgm6PnZTK01adYvrQpLTZSN4ekA"
  #  isDPlatformAdminOrgId: "1575170370317582336"
  isDPlatformAdminOrgId: "af1f01246cc6-ab00-084c-79e7-76f6022e"
  #  isMPlatformAdminOrgId: "1575170370317582337"
  isMPlatformAdminOrgId: "aa5422473bfb-aeea-9146-8ce7-04ff3c5e"
  pcwp1ContactUrl: "http://***************:7071"
  pcwp1PlanUrl: "http://***************:7073"
  profilesActive: "dev"
  pcwpPurchase: "http://***************:7073/json.rpc"
  isApiImportProductBradDispose: 2 # 1品牌自动新增（品牌是必传）2无品牌不保存，有品牌判断是否存在不存在报错
  templateFormUrl: "/templateForm"
  isShowCode: 1 # 是否显示开发中的代码
  isCountPlanOrderNum: 0 # 是否使用计划数量统计
  isNotRateAmount: 1 # 是否使用累加不含税总金额
  isLoginAuthQuery: 0 # 禁用登陆权限查询以加快启动
  isContractConsumeNum: 1 # 是否使用pcwp记录大宗月供合同数量
  isPlatformFee: 0 # 禁用平台计费功能以加快启动
  isZonePrice: 0 # 区域价格开发
  changAmountAndTaxPlanAmount: 0 # //2024年05月15号  应pcwp要求 Amount传不含税，NoTaxPlanAmount也要改成含税TaxPlanAmount
  selectMaterialMonthSupplyPlan: 1 # 是否使用完结计划  1 使用  0不使用
  ossPrefix: "http://*************:9022"
  isPCWPLogin: 0  # 禁用PCWP登录检查以加快启动
  isBusinessOrg: 1
  miniProgram: "http://*************:9050/pages/sheet/sheet?isExternal=true&id="
#  物资子公司自营店数据
  businessShopId: "1878734518961074177"
#  lowValueClass: ""
#  templateFormUrl: "/templateForm"
#  templateFormUrl: "C://aMySoft//templateForm"
#  templateFormUrl: "D://work//templateFormUrl"

seata:
  enabled: false
  application-id: mall-material
  tx-service-group: my_test_tx_group
  enable-auto-data-source-proxy: true
  registry:
    type: nacos
    nacos:
      application: seata-server
      server-addr: *************:8849
      group: SEATA_GROUP
      cluster: default
  #      namespace: eeba1f43-adcf-449f-bc82-754550cce91b
  config:
    type: nacos
    nacos:
      server-addr: *************:8849
      group: SEATA_GROUP
      namespace: 00393e11-71f6-417f-ba84-7afb3bf11a02
  service:
    vgroup-mapping:
      my_test_tx_group: default
    disable-global-transaction: false
  client:
    rm:
      report-success-enable: false
      lock:
        retry-interval: 20 #校验或占用全局锁重试间隔 默认10,单位毫秒
        retry-times: 60 #校验或占用全局锁重试次数 默认30
minio:
  endpoint: http://*************:9002 #Minio服务所在地址
  accessKey: minioadmin #访问的key
  secretKey: 9ImAtEIE #访问的秘钥
knife4j:
  basic:
    enable: true
    username: mall
    password: sj-lf@qjsk1.25
app:
  verify-code: false   #是否开启图形校验
  schedule:
    enable: false       #是否开启定时任务
logging:
  config: classpath:logback-spring-dev.xml
