server:
  port: 9010
  tomcat:
    uri-encoding: UTF-8
    max-swallow-size: -1 #内置tomcat限制文件最大大小 -1为不限制
spring:
  #管理文件上传和请求数据大小
  servlet:
    multipart:
      enabled: true
      max-file-size: 500MB  #单文件最大限制
      max-request-size: 500MB #总文件最大限制
      file-size-threshold: 500MB
  main:
    allow-bean-definition-overriding: true
  application:
    name: mall-material
  #本地环境
  profiles:
    #    active: "@activeProfile@"
#     active: prod
#     active: dev
      active: debug  # 使用debug配置以加快启动速度
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

#接口文档
knife4j:
  enable: true
  setting:
    enableSwaggerModels: true
    swaggerModelName: 实体类列表

#mybatisPlus配置
mybatis-plus:
  mapper-locations: classpath:/mapper/**/*.xml
  global-config:
    db-config:
      logic-delete-field: isDelete #默认deleted
      logic-delete-value: -1
      logic-not-delete-value: 0
      id-type: auto
#fegin调用超时时常
ribbon:
  OkToRetryOnAllOperations: false #对所有操作请求都进行重试,默认false
  ReadTimeout: 30000   #负载均衡超时时间，默认值5000
  ConnectTimeout: 30000 #ribbon请求连接的超时时间，默认值2000
  MaxAutoRetries: 0     #对当前实例的重试次数，默认0
  MaxAutoRetriesNextServer: 1 #对切换实例的重试次数，默认1

#商场配置
mall:
  type: 0 #物资商场为material(0)，设备商场为 device(1)
  isProductSearch: 0 #默认不走搜索引擎
  sendExchange: "SMS_Service" # 短信验证码发送交换机
  loginOutTime: 20 # 登陆过期时间
  isProductRepetitionCheck: 1
  isImportAutoFillBrandUnit: 1
  selfOrgName: "四川路桥建设集团股份有限公司物资分公司"

jwt:
  secret: a1g2y47dg3dj59fjhhsd7cnewy73j
  expiration: 604800

app:
  pcwp:
    reset-password: "111111"
    sys-code: msp
    identity-type: "5"
    org-header: "{\"orgId\":\"ad551eff9d03-8efe-2148-9ed8-64781e1e\",\"orgName\":\"四川路桥建设集团股份有限公司\",\"shortCode\":\"SRBC\",\"orgType\":1}"
  verify-code: false
  schedule:
    enable: false       #是否开启定时任务