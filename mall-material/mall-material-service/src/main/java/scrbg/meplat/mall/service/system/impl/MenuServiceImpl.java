package scrbg.meplat.mall.service.system.impl;

import com.scrbg.common.utils.PageUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.common.StringUtils;
import scrbg.meplat.mall.dto.system.SysMenu2DTO;
import scrbg.meplat.mall.entity.system.SysMenu2;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.system.SysMenu2Mapper;
import scrbg.meplat.mall.service.system.MenuService;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class MenuServiceImpl  implements MenuService {

    @Autowired
    private SysMenu2Mapper sysMenuMapper;

    @Override
    public List<SysMenu2> getMenuTree(Map<String, Object> params) {
        // 1. 查询所有菜单数据
        List<SysMenu2> allMenus = sysMenuMapper.getMenuTree(params);
        // 2. 构建树形结构
        return buildMenuTree(allMenus);
    }

    @Override
    public PageUtils<SysMenu2> getMenuList(Map<String, Object> params) {
        return null;
    }

    @Override
    public Boolean addMenu(SysMenu2DTO menu) {
        if(!StringUtils.isEmpty(menu.getPerms())){
            int count = sysMenuMapper.getPerms(null,menu.getPerms());
            if(count > 0){
                throw new BusinessException("存在重复的权限标记");
            }
        }
        return sysMenuMapper.addMenu(menu);
    }

    @Override
    public Boolean updateMenu(SysMenu2DTO menu) {
        if(!StringUtils.isEmpty(menu.getPerms())){
            int count = sysMenuMapper.getPerms(menu.getMenuId(),menu.getPerms());
            if(count > 0){
                throw new BusinessException("存在重复的权限标记");
            }
        }
        return sysMenuMapper.updateMenu(menu);
    }

    @Override
    public Boolean deleteMenu(SysMenu2DTO menu) {
        return sysMenuMapper.deleteMenu(menu);
    }

    @Override
    public SysMenu2 getMenuDetail(String menuId) {
        return null;
    }

    @Override
    public Boolean checkCodeExists(String code, String menuId) {
        return null;
    }

    private List<SysMenu2> buildMenuTree(List<SysMenu2> allMenus) {
        // 找出所有顶级菜单 (level为null或空字符串)
        return allMenus.stream()
                .filter(menu -> StringUtils.isEmpty(menu.getParentMenuId()))
                .peek(menu -> menu.setChildren(getChildren(menu.getMenuId(), allMenus)))
                .sorted(this::compareMenuSort)  // 使用自定义比较器
                .collect(Collectors.toList());
    }

    private List<SysMenu2> getChildren(String parentId, List<SysMenu2> allMenus) {
        return allMenus.stream()
                .filter(menu -> parentId.equals(menu.getParentMenuId()))
                .peek(menu -> menu.setChildren(getChildren(menu.getMenuId(), allMenus)))
                .sorted(this::compareMenuSort)
                .collect(Collectors.toList());
    }

    /**
     * 安全的菜单排序比较方法（处理null值情况）
     * @param m1 菜单1
     * @param m2 菜单2
     * @return 比较结果
     */
    private int compareMenuSort(SysMenu2 m1, SysMenu2 m2) {
        // 处理sort为null的情况（图片中显示排序是重要功能，建议数据库设置默认值）
        Integer sort1 = m1.getSort() != null ? m1.getSort() : Integer.MAX_VALUE;
        Integer sort2 = m2.getSort() != null ? m2.getSort() : Integer.MAX_VALUE;

        // 先按sort排序
        int sortCompare = sort1.compareTo(sort2);
        if (sortCompare != 0) {
            return sortCompare;
        }

        // 如果sort相同，再按菜单名称排序（根据图片中的"菜单名称"列）
        String title1 = m1.getTitle() != null ? m1.getTitle() : "";
        String title2 = m2.getTitle() != null ? m2.getTitle() : "";
        return title1.compareTo(title2);
    }
}
