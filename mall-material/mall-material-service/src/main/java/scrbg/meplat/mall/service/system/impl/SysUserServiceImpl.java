package scrbg.meplat.mall.service.system.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.crypto.digest.MD5;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.Md5Utils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import scrbg.meplat.mall.dto.system.UserPasswordDTO;
import scrbg.meplat.mall.entity.system.SysUser;
import scrbg.meplat.mall.entity.system.SysUserRole;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.service.system.SysUserService;
import scrbg.meplat.mall.mapper.system.SysUserMapper;
import scrbg.meplat.mall.mapper.system.SysUserRoleMapper;
import scrbg.meplat.mall.util.AESUtil;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService {



    private SysUserMapper sysUserMapper;

    private SysUserRoleMapper sysUserRoleMapper;


    @Autowired
    public void setSysUserMapper(SysUserMapper sysUserMapper) {
        this.sysUserMapper = sysUserMapper;
    }

    @Autowired
    public void setSysUserRoleMapper(SysUserRoleMapper sysUserRoleMapper) {
        this.sysUserRoleMapper = sysUserRoleMapper;
    }
    @Override
    public PageUtils<SysUser> queryPage(JSONObject jsonObject, LambdaQueryWrapper<SysUser> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keyword");
        List<String> orgCodes = (List<String>) innerMap.get("orgCodes");
        if (StringUtils.isNotBlank(keywords)) {
            queryWrapper.and((t) -> {
                t.like(SysUser::getUserName, keywords)
                        .or()
                        .like(SysUser::getUserNumber, keywords)
                        .or()
                        .like(SysUser::getUserMobile, keywords);

            });
        }
        if (!CollectionUtil.isEmpty(orgCodes)){
            queryWrapper.in(SysUser::getOrgId, orgCodes);
        }
        IPage<SysUser> page = this.page(
                new Query<SysUser>().getPage(jsonObject),
                queryWrapper
        );
        page.getRecords().forEach(user -> {
            List<SysUserRole> sysUserRoles = sysUserRoleMapper.selectListByUserId(user.getUserId());
            if (!CollectionUtil.isEmpty(sysUserRoles)) {
                //查询角色名称
                user.setRoleNames(null);
            }
        });
        return new PageUtils<SysUser>(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createUser(SysUser user) {
        this.save(user);
        //添加用户角色关联信息
        if (CollectionUtil.isNotEmpty(user.getRoleIds())) {
            sysUserRoleMapper.insertBatch(convertList(user.getRoleIds(),
                    roleId -> SysUserRole.init(user.getUserId(), roleId)));
        }
    }
    public static <T, U> List<U> convertList(Collection<T> from, Function<T, U> func) {
        if (CollUtil.isEmpty(from)) {
            return new ArrayList<>();
        }
        return from.stream().map(func).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public void syncUser(SysUser user) {
        this.save(user);
    }
    @Override
    public void updateUser(SysUser user) {
        String userId = user.getUserId();
        if(!CollectionUtil.isEmpty(user.getRoleIds())){
            Set<String> oldRoleIds = convertSet(sysUserRoleMapper.selectListByUserId(userId), SysUserRole::getRoleId);
            // 计算新增和删除的角色
            Set<String> neoRoleIds = CollUtil.emptyIfNull(user.getRoleIds());
            Collection<String> createRoleIds = CollUtil.subtract(neoRoleIds, oldRoleIds);
            Collection<String> deleteRoleIds = CollUtil.subtract(oldRoleIds, neoRoleIds);
            if (!CollectionUtil.isEmpty(createRoleIds)) {
                sysUserRoleMapper.insertBatch(convertList(createRoleIds,
                        roleId -> SysUserRole.init(userId, roleId)));
            }
            if (!CollectionUtil.isEmpty(deleteRoleIds)) {
                sysUserRoleMapper.deleteByUserIdAndRoleId(userId, deleteRoleIds);
            }
        }
        updateById(user);
    }
    public static <T, U> Set<U> convertSet(Collection<T> from, Function<T, U> func) {
        if (CollUtil.isEmpty(from)) {
            return new HashSet<>();
        }
        return from.stream().map(func).filter(Objects::nonNull).collect(Collectors.toSet());
    }
    @Override
    public void deleteUser(String id) {
        this.removeById(id);
    }

    @Override
    public SysUser getUserById(String id) {
        SysUser sysUser = this.getById(id);
        List<SysUserRole> sysUserRoles = sysUserRoleMapper.selectListByUserId(id);
        if(!CollectionUtil.isEmpty(sysUserRoles)){
            sysUser.setRoleIds(new HashSet<>());
            sysUserRoles.forEach(sysUserRole -> sysUser.getRoleIds().add(sysUserRole.getRoleId()));
        }
        return sysUser;
    }

    @Override
    public void resetPassword(UserPasswordDTO dto) {
        if(StringUtils.isBlank(dto.getNewPassword())){
            throw new BusinessException("新密码不能为空");
        }
//        SysUser oldUser = getUserById(dto.getUserId());
        // 校验旧密码密码
//        validateOldPassword(dto.getUserId(),dto.getOldPassword());
        // 执行更新
        SysUser updateObj = new SysUser();
        updateObj.setUserId(dto.getUserId());
        updateObj.setPassword(AESUtil.encrypt(dto.getNewPassword())); // 加密密码
        updateById(updateObj);
    }
    public void validateOldPassword(String userId,String oldPassword){
        SysUser sysUser = getById(userId);
        if(null == sysUser){
            throw new BusinessException("用户不存在");
        }
        if(!sysUser.getPassword().equals(AESUtil.encrypt(oldPassword))){
            throw new BusinessException("密码校验失败");
        }

    }
}
