<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="false" scanPeriod="60 seconds" debug="false">
    <springProperty scope="context" name="springAppName" source="spring.application.name"/>

    <!-- 简化的日志格式 -->
    <property name="log.pattern" value="%d{HH:mm:ss.SSS} %-5level %logger{36} - %msg%n"/>

    <!-- 控制台输出 - 只输出重要信息 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
        <!-- 过滤器 - 只显示WARN及以上级别 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>WARN</level>
        </filter>
    </appender>

    <!-- 应用日志 - 只记录应用相关的INFO日志 -->
    <appender name="app" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss} [%thread] %-5level %logger{20} - %msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 应用包的日志级别 -->
    <logger name="scrbg.meplat.mall" level="INFO" additivity="false">
        <appender-ref ref="app"/>
    </logger>

    <!-- 减少第三方库的日志输出 -->
    <logger name="org.springframework" level="WARN"/>
    <logger name="org.mybatis" level="WARN"/>
    <logger name="com.alibaba.druid" level="WARN"/>
    <logger name="org.apache.http" level="WARN"/>
    <logger name="org.springframework.cloud" level="WARN"/>
    <logger name="com.netflix" level="WARN"/>
    <logger name="org.springframework.boot.autoconfigure" level="WARN"/>
    <logger name="org.springframework.context" level="WARN"/>
    <logger name="org.springframework.beans" level="WARN"/>
    <logger name="org.springframework.web" level="WARN"/>
    <logger name="org.hibernate" level="WARN"/>
    <logger name="com.zaxxer.hikari" level="WARN"/>
    <logger name="io.lettuce" level="WARN"/>
    <logger name="org.redisson" level="WARN"/>

    <!-- Root日志配置 -->
    <root level="WARN">
        <appender-ref ref="console"/>
    </root>

</configuration>
