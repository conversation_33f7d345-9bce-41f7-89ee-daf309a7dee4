package scrbg.meplat.mall.service.impl.plan;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;

import scrbg.meplat.mall.dto.plan.PlanDTO;
import scrbg.meplat.mall.dto.plan.PlanExcelDto;
import scrbg.meplat.mall.entity.EnterpriseInfo;
import scrbg.meplat.mall.entity.OrderItem;
import scrbg.meplat.mall.entity.Orders;
import scrbg.meplat.mall.entity.Product;
import scrbg.meplat.mall.entity.ProductCategory;
import scrbg.meplat.mall.entity.ProductSku;
import scrbg.meplat.mall.entity.Shop;
import scrbg.meplat.mall.entity.ShoppingCart;
import scrbg.meplat.mall.entity.plan.Plan;
import scrbg.meplat.mall.entity.plan.PlanDetail;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.ShoppingCartMapper;
import scrbg.meplat.mall.mapper.plan.PlanMapper;
import scrbg.meplat.mall.service.EnterpriseInfoService;
import scrbg.meplat.mall.service.OrderItemService;
import scrbg.meplat.mall.service.OrdersService;
import scrbg.meplat.mall.service.ProductCategoryService;
import scrbg.meplat.mall.service.ProductService;
import scrbg.meplat.mall.service.ProductSkuService;
import scrbg.meplat.mall.service.ShopService;
import scrbg.meplat.mall.service.ShoppingCartService;
import scrbg.meplat.mall.service.plan.PlanDetailService;
import scrbg.meplat.mall.service.plan.PlanService;
import scrbg.meplat.mall.service.tt.OrgService;
import scrbg.meplat.mall.util.BillNumberGenerator;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;
import scrbg.meplat.mall.util.cart.CartValidationUtils;
import scrbg.meplat.mall.vo.product.ShoppingCartShopIdVO;

/**
 * @描述：采购计划主表 服务类
 * @作者: ye
 * @日期: 2025-05-27
 */
@Service
public class PlanServiceImpl extends ServiceImpl<PlanMapper, Plan> implements PlanService {

    @Autowired
    ProductService productService;

    @Autowired
    ShopService shopService;

    @Autowired
    ProductSkuService productSkuService;

    @Autowired
    EnterpriseInfoService enterpriseInfoService;

    @Autowired
    PlanDetailService planDetailService;

    @Autowired
    CartValidationUtils cartValidationUtils;

    @Autowired
    ShoppingCartMapper shoppingCartMapper;

    @Autowired
    private ShoppingCartService shoppingCartService;

    @Autowired
    ProductCategoryService productCategoryService;

    @Autowired
    private OrdersService ordersService;

    @Autowired
    private OrderItemService orderItemService;

    @Autowired
    private OrgService orgService;

//    @Autowired
//    PlanDetailService planDetailService;

    @Override
    public PageUtils<Plan> queryPage(PlanDTO dto) {
        LambdaQueryChainWrapper<Plan> query = this.lambdaQuery();
        // 计划创建人员查询条件
//        if (dto.getRecorderId() != null) {
//            queryWrapper.eq("founder_id", dto.getRecorderId());
//        }
        // 添加type类型查询条件
        if (dto.getType() != null) {
            query.eq(Plan::getType, dto.getType());
        }
        // 添加billNo计划编号查询条件
        if (dto.getBillNo() != null && !dto.getBillNo().trim().isEmpty()) {
            // 模糊查询
            query.like(Plan::getBillNo, dto.getBillNo());
        }
        // 添加state类型查询条件
        if (dto.getState() != null) {
            query.eq(Plan::getState, dto.getState());
        }
        UserLogin user =ThreadLocalUtil.getCurrentUser();
        if (dto.isSub()) {
            List<String> subOrgIds = orgService.getSubOrgIds(user.getOrgId());
            query.in(Plan::getOrgId, Stream.concat(subOrgIds.stream(), Stream.of(user.getOrgId()))
                                     .collect(Collectors.toList()));
        }else {
            query.eq(Plan::getOrgId, user.getOrgId());
        }
        // 按创建时间倒序排列
        query.orderByDesc(Plan::getGmtCreate);
        IPage<Plan> page = query.page(new Page<>(dto.getPage(), dto.getLimit()));
        return new PageUtils<>(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(Plan plan) {
        super.save(plan);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Plan plan) {
        super.updateById(plan);
    }

    @Override
    public Plan getById(String id) {

        Plan plan = super.getById(id);
        if (plan != null) {
            // 查询计划明细信息
            List<PlanDetail> planDetails = planDetailService.lambdaQuery()
                    .eq(PlanDetail::getBillId, id)  // 使用billId作为关联条件
                    .orderByAsc(PlanDetail::getGmtCreate)  // 修正：使用继承的gmtCreate字段排序
                    .list();

            // 设置明细信息到主表对象中
            plan.setDetails(planDetails);
        }
        return plan;
    }

    /**
     * 根据计划编号获取计划信息
     *
     * @param billId 计划编号
     * @return 计划信息
     */
    @Override
    public Plan getByBillId(String billId) {
        Plan plan = lambdaQuery().eq(Plan::getBillId, billId).one();
        return plan;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        super.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBatch(List<String> ids) {
        super.removeByIds(ids);
    }


    /**
     * 根据购物车信息直接下单生成计划主表和计划明细表
     *
     * @return 计划创建结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Plan createMaterialPlanByCartIds(List<ShoppingCartShopIdVO> cartVOS) {

        List<PlanDetail> planDetails = new ArrayList<PlanDetail>();

        Plan plan = mapToPlan(cartVOS);
        planDetails = plan.getDetails();

        // 4. 保存计划
        savePlanAndDetails(plan, planDetails);
        return plan;
    }

    public Plan mapToPlan(List<ShoppingCartShopIdVO> cartVOS) {

        UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
        ShoppingCart shoppingCart = cartVOS.get(0).getShoppingCarts().get(0);
        Integer productType = shoppingCart.getProductType();
        Product product = productService.getById(shoppingCart.getProductId());
        List<PlanDetail> planDetails = new ArrayList<PlanDetail>();

        // 1. 创建计划主表
        Plan plan = createPlanHeader(productType, currentUser);
        plan.setTaxRate(product.getTaxRate());
        // 2. 创建计划明细表
        planDetails = createPlanDetails(plan, cartVOS);

        // 3. 计算并更新计划金额
        updatePlanAmounts(plan, planDetails);
        plan.setDetails(planDetails);
        return plan;
    }

    /**
     * 创建计划主表
     *
     * @param productType 商品类型
     * @param currentUser 当前用户
     * @return 计划主表
     */
    private Plan createPlanHeader(int productType, UserLogin currentUser) {
        Plan plan = new Plan();

        // 生成计划ID和编号plan
        plan.setBillId(UUID.randomUUID().toString().replace("-", ""));
        plan.setBillNo(BillNumberGenerator.generatePlanNumber(productType));

        // 设置计划状态和日期
        plan.setState("1"); // 1待审核
        plan.setBillDate(new Date());

        // 设置计划类型
        plan.setType(productType);

        // 设置组织机构信息
        plan.setOrgId(currentUser.getOrgId());
        plan.setOrgName(currentUser.getEnterpriseName());
        plan.setOrgShort(currentUser.getEnterpriseName());

        // 初始化金额（后续计算）
        plan.setPlanAmount(BigDecimal.ZERO);
        plan.setTaxPlanAmount(BigDecimal.ZERO);
        plan.setTaxAmount(BigDecimal.ZERO);

        return plan;
    }

    /**
     * 创建计划明细表
     *
     * @param plan    计划主表
     * @param cartVOS 购物车数据
     * @return 计划明细列表
     */
    private List<PlanDetail> createPlanDetails(Plan plan, List<ShoppingCartShopIdVO> cartVOS) {
        List<PlanDetail> planDetails = new ArrayList<>();

        for (ShoppingCartShopIdVO cartVO : cartVOS) {
            // 获取店铺和供应商信息
            Shop shop = shopService.getById(cartVO.getShopId());
            EnterpriseInfo enterpriseInfo = enterpriseInfoService.getById(shop.getEnterpriseId());

            for (ShoppingCart cart : cartVO.getShoppingCarts()) {

                Product product = productService.getById(cart.getProductId());
                ProductSku productSku = productSkuService.getById(cart.getSkuId());
                ProductCategory productCategory = productCategoryService.getById(product.getClassId());
                PlanDetail planDetail = new PlanDetail();

                // 设置计划明细ID和关联计划ID
                planDetail.setBillId(plan.getBillId());

                // 设置账期
                planDetail.setPaymentPeriod(cart.getPaymentPeriod());

                // 设置物料信息
                planDetail.setMaterialId(product.getClassId());
                planDetail.setMaterialName(productCategory.getClassName());
                planDetail.setSpec(productSku.getSkuName() != null ? productSku.getSkuName() : "");
                planDetail.setTexture(product.getProductTexture() != null ? product.getProductTexture() : "");
                planDetail.setUnit(productSku.getUnit());
                planDetail.setMaterialUse("默认");

                // 设置物料分类信息
                planDetail.setMaterialClassId(productCategory.getClassId());
                planDetail.setMaterialClassName(productCategory.getClassName());
                planDetail.setTopClassId("top");
                planDetail.setTopClassName("top");

                // 设置商品信息
                planDetail.setTradeId(product.getProductId());
                planDetail.setTradeName(product.getProductName());

                // 设置数量和价格
                BigDecimal price = shoppingCartService.getSellPrice(cart);
                planDetail.setQuantity(cart.getCartNum().intValue());
                planDetail.setPrice(calculateNoTaxPrice(price, product.getTaxRate()));
                planDetail.setTaxPrice(price);

                // 计算金额
                BigDecimal noTaxAmount = planDetail.getPrice().multiply(new BigDecimal(planDetail.getQuantity()));
                BigDecimal taxAmount = price.multiply(new BigDecimal(planDetail.getQuantity()));
                BigDecimal notConsumeAmount = planDetail.getTaxPrice().multiply(new BigDecimal(planDetail.getQuantity()))
                        .setScale(2, RoundingMode.HALF_UP);//含税金额 = 含税单价 * 数量
                BigDecimal taxFee = taxAmount.subtract(noTaxAmount);

                planDetail.setAmount(noTaxAmount);
                planDetail.setTaxAmount(taxFee);//税额
                planDetail.setConsumeAmount(BigDecimal.ZERO);//已消耗金额
                planDetail.setNotConsumeAmount(notConsumeAmount);//未消耗金额
                planDetail.setConsumeNumber(0);//已消耗数量
                planDetail.setNotConsumeNumber(planDetail.getQuantity());//未消耗数量

                // 设置店铺和供应商信息
                planDetail.setShopId(shop.getShopId());
                planDetail.setShopName(shop.getShopName());
                planDetail.setStorageId(shop.getEnterpriseId());
                planDetail.setStorageName(enterpriseInfo.getEnterpriseName());
                planDetail.setOrgShort(enterpriseInfo.getShortCode());
                planDetail.setStorageOrgId("");
                planDetail.setCreditCode(enterpriseInfo.getSocialCreditCode());
                //统一社会信用代码，如果企业统一社会信用代码信息为空设置空
                if (enterpriseInfo.getSocialCreditCode() == null || enterpriseInfo.getSocialCreditCode().isEmpty()) {
                    planDetail.setCreditCode(" ");
                }
                planDetails.add(planDetail);
            }
        }
        return planDetails;
    }

    /**
     * 更新计划金额
     *
     * @param plan        计划主表
     * @param planDetails 计划明细列表
     */
    private void updatePlanAmounts(Plan plan, List<PlanDetail> planDetails) {
        BigDecimal totalPlanAmount = BigDecimal.ZERO;
        BigDecimal totalTaxAmount = BigDecimal.ZERO;

        for (PlanDetail detail : planDetails) {
            totalPlanAmount = totalPlanAmount.add(detail.getAmount());
            totalTaxAmount = totalTaxAmount.add(detail.getTaxAmount());
        }

        plan.setPlanAmount(totalPlanAmount);
        plan.setTaxAmount(totalTaxAmount);
        plan.setTaxPlanAmount(totalPlanAmount.add(totalTaxAmount));
    }

    /**
     * 保存计划主表和明细表
     *
     * @param plan        计划主表
     * @param planDetails 计划明细列表
     */
    public void savePlanAndDetails(Plan plan, List<PlanDetail> planDetails) {
        // 保存计划主表
        boolean planSaved = save(plan);
        if (!planSaved) {
            throw new BusinessException("计划主表保存失败！");
        }

        // 批量保存计划明细
        if (!CollectionUtils.isEmpty(planDetails)) {
            boolean detailsSaved = planDetailService.saveBatch(planDetails);
            if (!detailsSaved) {
                throw new BusinessException("计划明细表保存失败！");
            }
        }
    }

    /**
     * 计算不含税价格
     *
     * @param taxPrice 含税价格
     * @param taxRate  税率
     * @return 不含税价格
     */
    private BigDecimal calculateNoTaxPrice(BigDecimal taxPrice, BigDecimal taxRate) {
        if (taxPrice == null || taxRate == null) {
            return taxPrice;
        }

        // 不含税价格 = 含税价格 / (1 + 税率)
        BigDecimal divisor = BigDecimal.ONE.add(taxRate.divide(new BigDecimal("100")));
        return taxPrice.divide(divisor, 2, RoundingMode.HALF_UP);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deletePlanAndDetails(String id) {
        Plan plan = this.getByBillId(id);
        if (plan==null) {
            throw new BusinessException("计划不存在");
        }
        boolean del = this.lambdaUpdate().eq(Plan::getBillId, id).remove();
        if (!del) {
            throw new BusinessException("系统异常");
        }
        List<PlanDetail> planDetails = planDetailService.lambdaQuery().eq(PlanDetail::getBillId, id).list();
        planDetailService.lambdaUpdate().eq(PlanDetail::getBillId, id).remove();
        // 删除订单数据
        List<Orders> orders = ordersService.lambdaQuery().eq(Orders::getPlanId, id).list();
        if (orders.isEmpty()) {
            return;
        }
        orderItemService.lambdaUpdate()
                            .in(OrderItem::getOrderId, orders.stream().map(Orders::getOrderId).collect(Collectors.toList())).remove();
        ordersService.lambdaUpdate().eq(Orders::getPlanId, id).remove();
        // 返还库存
        ArrayList<ProductSku> productSkus = new ArrayList<>();
        for (PlanDetail planDetail : planDetails) {
            ProductSku productSku = productSkuService.getByProductId(planDetail.getTradeId());
            BigDecimal newStock = productSku.getStock().add(new BigDecimal(planDetail.getQuantity()));
            ProductSku p = new ProductSku();
            p.setSkuId(productSku.getSkuId());
            p.setStock(newStock);
            productSkus.add(p);
        }
        if (!productSkus.isEmpty()) {
            boolean updateResult = productSkuService.updateBatchById(productSkus);
            if (!updateResult) {
                throw new BusinessException(400, "删除失败!");
            }
        }
    }

    @Override
    public PlanExcelDto mapToPlanExcel(Plan plan) {
        PlanExcelDto dto = new PlanExcelDto();
        dto.setBillId(plan.getBillId());
        dto.setBillNo(plan.getBillNo());
        dto.setPBillId(plan.getPBillId());
        dto.setPBillNo(plan.getPBillNo());
        dto.setState(plan.getState());
        dto.setBillDate(plan.getBillDate());
        dto.setType(plan.getType());
        dto.setOrgId(plan.getOrgId());
        dto.setOrgName(plan.getOrgName());
        dto.setOrgShort(plan.getOrgShort());
        dto.setPlanAmount(plan.getPlanAmount());
        dto.setTaxPlanAmount(plan.getTaxPlanAmount());
        dto.setTaxAmount(plan.getTaxAmount());
        dto.setRemark(plan.getRemark());
        return dto;
    }

}
