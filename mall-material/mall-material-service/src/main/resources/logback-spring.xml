<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <springProperty scope="context" name="springAppName" source="spring.application.name"/>

    <!-- 定义参数常量 -->
    <property name="log.level" value="WARN"/>  <!-- 改为WARN级别以减少日志输出 -->
    <property name="log.path" value="logs/material"/>
    <property name="log.maxHistory" value="999"/>
    <property name="log.maxFileSize" value="10MB"/>
    <property name="log.totalSizeCap" value="100GB"/>
    <!-- 简化日志格式以提高性能 -->
    <property name="log.pattern" value="%d{HH:mm:ss.SSS} %-5level %logger{36} - %msg%n"/>

    <!-- 输出到控制台 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
    </appender>

    <!-- INFO 日志文件输出 -->
    <appender name="file_info" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/info/info.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>${log.maxHistory}</maxHistory>
            <maxFileSize>${log.maxFileSize}</maxFileSize>
            <totalSizeCap>${log.totalSizeCap}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- ERROR 日志文件输出 -->
    <appender name="file_error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/error/error.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>${log.maxHistory}</maxHistory>
            <maxFileSize>${log.maxFileSize}</maxFileSize>
            <totalSizeCap>${log.totalSizeCap}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 添加第三方库日志级别控制 -->
    <logger name="org.springframework" level="WARN"/>
    <logger name="org.mybatis" level="WARN"/>
    <logger name="com.alibaba.druid" level="WARN"/>
    <logger name="org.apache.http" level="WARN"/>
    <logger name="org.springframework.cloud" level="WARN"/>
    <logger name="com.netflix" level="WARN"/>
    <logger name="org.springframework.boot.autoconfigure" level="WARN"/>
    <logger name="org.springframework.context" level="WARN"/>
    <logger name="org.springframework.beans" level="WARN"/>
    <logger name="org.springframework.web" level="WARN"/>
    <logger name="io.lettuce" level="WARN"/>
    <logger name="com.zaxxer.hikari" level="WARN"/>

    <!-- 应用日志保持INFO级别 -->
    <logger name="scrbg.meplat.mall" level="INFO"/>

    <!-- Root 日志配置 - 改为WARN级别 -->
    <root level="warn">
        <appender-ref ref="console"/>
        <appender-ref ref="file_info"/>
        <appender-ref ref="file_error"/>
    </root>
</configuration>
