package scrbg.meplat.mall.controller.system;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.config.auth.IsRole;
import scrbg.meplat.mall.config.auth.RoleEnum;
import scrbg.meplat.mall.dto.system.UserPasswordDTO;
import scrbg.meplat.mall.entity.User;
import scrbg.meplat.mall.entity.system.SysUser;
import scrbg.meplat.mall.service.ShopService;
import scrbg.meplat.mall.service.UserService;
import scrbg.meplat.mall.service.system.SysUserService;

import javax.validation.Valid;

@RestController
@RequestMapping("/platform/neoUser")
@ApiSort(value = 500)
@Api(tags = "用户接口-")
public class NeoUserController {

    @Autowired
    public SysUserService userService;


    @PostMapping("/listByParameters")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "keyword", value = "账号", dataTypeClass = String.class),
    })
    public PageR<SysUser> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils<SysUser> page = userService.queryPage(jsonObject, new LambdaQueryWrapper<>());
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<SysUser> findById(String userId) {
        SysUser user = userService.getUserById(userId);
        return R.success(user);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R<Object> save(@RequestBody SysUser user) {
        userService.createUser(user);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R<Object> update(@RequestBody SysUser user) {
        userService.updateUser(user);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<Object> delete(String id) {
        userService.deleteUser(id);
        return R.success();
    }
    @PostMapping("/resetPassword")
    @ApiOperation(value = "重置密码")
    public R<Object> resetPassword(@Valid @RequestBody UserPasswordDTO passwordDTO) {
        userService.resetPassword(passwordDTO);
        return R.success();
    }

}
