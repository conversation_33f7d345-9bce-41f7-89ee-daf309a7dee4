package scrbg.meplat.mall.service.system;

import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.dto.system.SysMenu2DTO;
import scrbg.meplat.mall.entity.system.SysMenu2;

import java.util.List;
import java.util.Map;

public interface MenuService {
    List<SysMenu2> getMenuTree(Map<String, Object> params);
    PageUtils<SysMenu2> getMenuList(Map<String, Object> params);
    Boolean addMenu(SysMenu2DTO menu);
    Boolean updateMenu(SysMenu2DTO menu);
    Boolean deleteMenu(SysMenu2DTO menu);
    SysMenu2 getMenuDetail(String menuId);
    Boolean checkCodeExists(String code, String menuId);
}
