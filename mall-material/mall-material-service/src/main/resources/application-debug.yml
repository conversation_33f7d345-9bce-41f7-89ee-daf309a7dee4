# Debug专用配置 - 优化启动速度
spring:
  profiles:
    active: debug
  jmx:
    enabled: false  # 禁用JMX
  devtools:
    restart:
      enabled: false  # 禁用热重启
    livereload:
      enabled: false  # 禁用LiveReload
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.admin.SpringApplicationAdminJmxAutoConfiguration
      - org.springframework.boot.autoconfigure.jmx.JmxAutoConfiguration
      - org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration
      - org.springframework.boot.actuate.autoconfigure.metrics.export.simple.SimpleMetricsExportAutoConfiguration

# 数据库配置 - 继承dev环境
datasource:
  dynamic:
    primary: master
    strict: false
    datasource:
      master:
        url: ************************************************************************************************************************************************************************************
        username: root
        password: 123456
        driver-class-name: com.mysql.cj.jdbc.Driver
        type: com.alibaba.druid.pool.DruidDataSource
        druid:
          initial-size: 1  # 减少初始连接数
          min-idle: 1      # 减少最小空闲连接数
          max-active: 5    # 减少最大活跃连接数
          max-wait: 60000
          time-between-eviction-runs-millis: 60000
          min-evictable-idle-time-millis: 300000
          validation-query: SELECT 1 FROM DUAL
          test-while-idle: true
          test-on-borrow: false
          test-on-return: false
          pool-prepared-statements: false
          filters: stat,wall
          web-stat-filter:
            enabled: false  # 禁用Web统计
          stat-view-servlet:
            enabled: false  # 禁用监控页面

# Redis配置
redis:
  host: *************
  port: 6379
  password: 123456
  timeout: 3000ms
  lettuce:
    pool:
      max-active: 5   # 减少连接池大小
      max-idle: 3
      min-idle: 1
      max-wait: 3000ms

# 日志配置 - 减少日志输出
logging:
  level:
    root: WARN
    scrbg.meplat.mall: INFO
    org.springframework: WARN
    org.mybatis: WARN
    com.alibaba.druid: WARN
    org.apache.http: WARN
    org.springframework.cloud: WARN
    com.netflix: WARN

# MyBatis配置
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl  # 禁用SQL日志

# 商场配置 - 继承基础配置
mall:
  type: 0
  isProductSearch: 0
  sendExchange: "SMS_Service"
  loginOutTime: 20
  isProductRepetitionCheck: 1
  isImportAutoFillBrandUnit: 1
  selfOrgName: "四川路桥建设集团股份有限公司物资分公司"
  prodPcwp2Url: "http://**************:15101"
  prodPcwp2Url02: "http://**************:15103"
  thirdApiToken: "UXp0UjJET2csMTY3MTA0MjIzMw.H83JNfMc8fze5OF-Vn4oSF4SqF4Wgm6PnZTK01adYvrQpLTZSN4ekA"
  isDPlatformAdminOrgId: "af1f01246cc6-ab00-084c-79e7-76f6022e"
  isMPlatformAdminOrgId: "aa5422473bfb-aeea-9146-8ce7-04ff3c5e"
  pcwp1ContactUrl: "http://171.221.203.162:7071"
  pcwp1PlanUrl: "http://171.221.203.162:7073"
  profilesActive: "debug"
  pcwpPurchase: "http://171.221.203.162:7073/json.rpc"
  isApiImportProductBradDispose: 2
  templateFormUrl: "/templateForm"
  isShowCode: 1
  isCountPlanOrderNum: 0
  isNotRateAmount: 1
  isLoginAuthQuery: 0  # 禁用登录权限查询以加快启动
  isContractConsumeNum: 1
  isPlatformFee: 0     # 禁用平台计费功能
  isZonePrice: 0
  changAmountAndTaxPlanAmount: 0
  selectMaterialMonthSupplyPlan: 1
  ossPrefix: "http://192.168.91.16:9022"
  isPCWPLogin: 0       # 禁用PCWP登录检查
  isBusinessOrg: 1

# 禁用Actuator
management:
  endpoints:
    enabled-by-default: false

# 禁用Knife4j
knife4j:
  enable: false
  production: true
