package scrbg.meplat.mall.mapper.system;

import org.apache.ibatis.annotations.Mapper;
import scrbg.meplat.mall.dto.system.SysRole2DTO;
import scrbg.meplat.mall.entity.system.SysJg;
import scrbg.meplat.mall.entity.system.SysRole2;

import java.util.List;


@Mapper
public interface Role2Mapper {
    List<SysRole2> selectRoleList(SysRole2DTO role);
    int countRoleList(SysRole2DTO role);
    int insertRole(SysRole2DTO role);
    int updateRole(SysRole2DTO role);
    int deleteRole(String roleId, String modifyTime, String modifyUserId, String modifyUserName);
    SysRole2 selectRoleById(String roleId);
    List<SysJg> getAllJglist();
}
