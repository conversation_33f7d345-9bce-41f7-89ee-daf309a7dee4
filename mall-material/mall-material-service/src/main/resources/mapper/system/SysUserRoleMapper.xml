<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="scrbg.meplat.mall.mapper.system.SysUserRoleMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="scrbg.meplat.mall.entity.system.SysUserRole">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="role_id" property="roleId" jdbcType="VARCHAR"/>
    </resultMap>

    <insert id="insertBatch">
        INSERT INTO sys_user_role (user_id, role_id) VALUES
        <foreach collection="userRoles" item="item" separator=",">
            (#{item.userId}, #{item.roleId})
        </foreach>
    </insert>

    <delete id="deleteByUserIdAndRoleId">
        delete from sys_user_role where user_id = #{userId} and roleId in #{roleIds}
    </delete>
    <select id="selectListByUserId" resultMap="BaseResultMap">
        select * from sys_user_role where user_id = #{userId}
    </select>
</mapper>