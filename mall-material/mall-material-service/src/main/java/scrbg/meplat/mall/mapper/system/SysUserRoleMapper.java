package scrbg.meplat.mall.mapper.system;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import scrbg.meplat.mall.entity.system.SysUserRole;

import java.util.Collection;
import java.util.List;


@Mapper
@Repository
public interface SysUserRoleMapper extends BaseMapper<SysUserRole> {
    void insertBatch(@Param("userRoles") List<SysUserRole> roles);

    void deleteByUserIdAndRoleId(@Param("userId") String userId,@Param("roleIds") Collection<String> deleteRoleIds);


    List<SysUserRole> selectListByUserId(@Param("userId")String userId);
}
