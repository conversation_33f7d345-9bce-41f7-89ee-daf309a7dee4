package scrbg.meplat.mall.vo.product.website;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.vo.product.website.material.WMaterialBaseVo;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-11-04 16:56
 */
@Data
public class WCartInfoVO   {

    @ApiModelProperty(value = "店铺id")
    private String shopId;

    @ApiModelProperty(value = "店铺名称")
    private String shopName;

    @ApiModelProperty(value = "店铺状态 1:启用 0停用")
    private Integer shopState;

    @ApiModelProperty(value = "商品列表信息")
    private List<WCartItemVO> productInfo;

    @ApiModelProperty(value = "总金额（放在数组第一个）")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "是否选中")
    private Boolean checked;


    @ApiModelProperty(value = "提交文字（放在数组第一个）")
    private String submitStr;
}
